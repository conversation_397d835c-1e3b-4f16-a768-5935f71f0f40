import 'package:toii_social/core/service/user_service.dart';
import 'package:toii_social/model/base/base_response.dart';
import 'package:toii_social/model/user/user_stats_model.dart';

abstract class UserRepository {
  Future<BaseResponse<UserStatsModel>> getUserStats(String userId);
}

class UserRepositoryImpl extends UserRepository {
  final UserService userService;

  UserRepositoryImpl({required this.userService});

  @override
  Future<BaseResponse<UserStatsModel>> getUserStats(String userId) async {
    try {
      return await userService.getUserStats(userId);
    } catch (e) {
      rethrow;
    }
  }
}
