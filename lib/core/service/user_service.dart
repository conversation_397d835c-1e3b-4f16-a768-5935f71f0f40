import 'package:dio/dio.dart';
import 'package:retrofit/retrofit.dart';
import 'package:toii_social/model/base/base_response.dart';
import 'package:toii_social/model/user/user_stats_model.dart';

part 'user_service.g.dart';

@RestApi()
abstract class UserService {
  factory UserService(Dio dio, {String baseUrl}) = _UserService;

  @GET('/user/api/v1/stats/users/{user_id}')
  Future<BaseResponse<UserStatsModel>> getUserStats(
    @Path('user_id') String userId,
  );
}
