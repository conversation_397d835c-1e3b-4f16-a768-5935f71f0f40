part of 'user_stats_cubit.dart';

enum UserStatsStatus { initial, loading, success, failure }

extension UserStatsX on UserStatsStatus {
  bool get isInitial => this == UserStatsStatus.initial;
  bool get isLoading => this == UserStatsStatus.loading;
  bool get isSuccess => this == UserStatsStatus.success;
  bool get isFailure => this == UserStatsStatus.failure;
}

final class UserStatsState {
  final UserStatsModel? userStats;
  final UserStatsStatus status;
  final String? errorMessage;

  const UserStatsState({
    this.userStats,
    this.status = UserStatsStatus.initial,
    this.errorMessage,
  });

  UserStatsState copyWith({
    UserStatsModel? userStats,
    UserStatsStatus? status,
    String? errorMessage,
  }) {
    return UserStatsState(
      userStats: userStats ?? this.userStats,
      status: status ?? this.status,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }
}
