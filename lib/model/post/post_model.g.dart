// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'post_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PostResponseDataModel _$PostResponseDataModelFromJson(
        Map<String, dynamic> json) =>
    PostResponseDataModel(
      posts: (json['posts'] as List<dynamic>)
          .map((e) => PostModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      total: (json['total'] as num?)?.toInt() ?? 0,
    );

Map<String, dynamic> _$PostResponseDataModelToJson(
        PostResponseDataModel instance) =>
    <String, dynamic>{
      'posts': instance.posts,
      'total': instance.total,
    };

PostModel _$PostModelFromJson(Map<String, dynamic> json) => PostModel(
      id: json['id'] as String,
      user: json['user'] == null
          ? null
          : UserModel.fromJson(json['user'] as Map<String, dynamic>),
      content: json['content'] as String? ?? '',
      mediaKeys: (json['media_keys'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      mediaUrls: (json['media_urls'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      mediaDetails: (json['media_details'] as List<dynamic>?)
              ?.map(
                  (e) => MediaDetailsModel.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      reposts: (json['reposts'] as num?)?.toInt() ?? 0,
      comments: (json['comments'] as num?)?.toInt() ?? 0,
      reactions: json['reactions'] == null
          ? null
          : ReactionGroupModel.fromJson(
              json['reactions'] as Map<String, dynamic>),
      privacy: json['privacy'] as String?,
      createdAt: json['created_at'] as String?,
      updatedAt: json['updated_at'] as String?,
      isRepost: json['is_repost'] as bool?,
    );

Map<String, dynamic> _$PostModelToJson(PostModel instance) => <String, dynamic>{
      'id': instance.id,
      'user': instance.user,
      'content': instance.content,
      'media_keys': instance.mediaKeys,
      'media_urls': instance.mediaUrls,
      'media_details': instance.mediaDetails,
      'reposts': instance.reposts,
      'comments': instance.comments,
      'reactions': instance.reactions,
      'privacy': instance.privacy,
      'created_at': instance.createdAt,
      'updated_at': instance.updatedAt,
      'is_repost': instance.isRepost,
    };

CreatePostRequestModel _$CreatePostRequestModelFromJson(
        Map<String, dynamic> json) =>
    CreatePostRequestModel(
      content: json['content'] as String?,
      privacy: json['privacy'] as String,
      mediaKeys: (json['media_keys'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
    );

Map<String, dynamic> _$CreatePostRequestModelToJson(
        CreatePostRequestModel instance) =>
    <String, dynamic>{
      'content': instance.content,
      'media_keys': instance.mediaKeys,
      'privacy': instance.privacy,
    };

MediaDetailsModel _$MediaDetailsModelFromJson(Map<String, dynamic> json) =>
    MediaDetailsModel(
      id: json['id'] as String?,
      userId: json['user_id'] as String?,
      type: json['type'] as String?,
      fileName: json['file_name'] as String?,
      bucket: json['bucket'] as String?,
      key: json['key'] as String?,
      s3Url: json['s3_url'] as String?,
      cdnUrl: json['cdn_url'] as String?,
      size: (json['size'] as num?)?.toInt(),
      contentType: json['content_type'] as String?,
      imageVariants: (json['image_variants'] as List<dynamic>?)
              ?.map((e) => ImageVariants.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      originalWidth: (json['original_width'] as num?)?.toInt(),
      originalHeight: (json['original_height'] as num?)?.toInt(),
      createdAt: json['created_at'] as String?,
      updatedAt: json['updated_at'] as String?,
    );

Map<String, dynamic> _$MediaDetailsModelToJson(MediaDetailsModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'user_id': instance.userId,
      'type': instance.type,
      'file_name': instance.fileName,
      'bucket': instance.bucket,
      'key': instance.key,
      's3_url': instance.s3Url,
      'cdn_url': instance.cdnUrl,
      'size': instance.size,
      'content_type': instance.contentType,
      'image_variants': instance.imageVariants,
      'original_width': instance.originalWidth,
      'original_height': instance.originalHeight,
      'created_at': instance.createdAt,
      'updated_at': instance.updatedAt,
    };

ImageVariants _$ImageVariantsFromJson(Map<String, dynamic> json) =>
    ImageVariants(
      size: json['size'] as String?,
      width: (json['width'] as num?)?.toInt(),
      height: (json['height'] as num?)?.toInt(),
      s3Url: json['s3_url'] as String?,
      cdnUrl: json['cdn_url'] as String?,
      fileSize: (json['fileSize'] as num?)?.toInt(),
    );

Map<String, dynamic> _$ImageVariantsToJson(ImageVariants instance) =>
    <String, dynamic>{
      'size': instance.size,
      'width': instance.width,
      'height': instance.height,
      's3_url': instance.s3Url,
      'cdn_url': instance.cdnUrl,
      'fileSize': instance.fileSize,
    };
