import 'package:json_annotation/json_annotation.dart';

part 'user_stats_model.g.dart';

@JsonSerializable()
class UserStatsModel {
  @Json<PERSON>ey(name: 'followers_count')
  final int followersCount;
  
  @<PERSON><PERSON><PERSON><PERSON>(name: 'following_count')
  final int followingCount;
  
  @<PERSON><PERSON><PERSON><PERSON>(name: 'friends_count')
  final int friendsCount;
  
  @<PERSON><PERSON><PERSON><PERSON>(name: 'user_id')
  final String userId;

  const UserStatsModel({
    required this.followersCount,
    required this.followingCount,
    required this.friendsCount,
    required this.userId,
  });

  factory UserStatsModel.fromJson(Map<String, dynamic> json) =>
      _$UserStatsModelFromJson(json);

  Map<String, dynamic> toJson() => _$UserStatsModelToJson(this);

  UserStatsModel copyWith({
    int? followersCount,
    int? followingCount,
    int? friendsCount,
    String? userId,
  }) {
    return UserStatsModel(
      followersCount: followersCount ?? this.followersCount,
      followingCount: followingCount ?? this.followingCount,
      friendsCount: friendsCount ?? this.friendsCount,
      userId: userId ?? this.userId,
    );
  }
}
