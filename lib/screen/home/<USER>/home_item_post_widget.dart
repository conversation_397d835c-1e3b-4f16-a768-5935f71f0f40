import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:go_router/go_router.dart';
import 'package:readmore/readmore.dart';
import 'package:toii_social/gen/assets.gen.dart';
import 'package:toii_social/model/post/post_model.dart';
import 'package:toii_social/router/app_router.dart';
import 'package:toii_social/screen/home/<USER>/home_details_screen.dart';
import 'package:toii_social/screen/home/<USER>/post/post_action_bar.dart';
import 'package:toii_social/screen/home/<USER>/post/post_header.dart';
import 'package:toii_social/screen/home/<USER>/post/post_image_gallery.dart';
import 'package:toii_social/utils/time_utils.dart';
import 'package:toii_social/widget/colors/colors.dart';
import 'package:toii_social/widget/colors/text_style.dart';
import 'package:toii_social/widget/images/avatar_widget.dart';

class HomeItemPostWidget extends StatefulWidget {
  final PostModel post;
  final bool isNotOnTap;
  final bool isShowActionMore;
  const HomeItemPostWidget({
    super.key,
    required this.post,
    required this.isNotOnTap,
    required this.isShowActionMore,
  });

  @override
  State<HomeItemPostWidget> createState() => _HomeItemPostWidgetState();
}

class _HomeItemPostWidgetState extends State<HomeItemPostWidget> {
  bool _isMainContentExpanded = false;
  bool _isRepostContentExpanded = false;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    // Check if this is a repost
    if (widget.post.isRepost == true) {
      return _buildRepostLayout(context, theme);
    } else {
      return _buildNormalLayout(context, theme);
    }
  }

  Widget _buildNormalLayout(BuildContext context, ThemeData theme) {
    return Stack(
      alignment: Alignment.bottomCenter,
      children: [
        GestureDetector(
          behavior: HitTestBehavior.opaque,
          onTap: () {
            if (!widget.isNotOnTap) {
              context.push(
                RouterEnums.homeDetails.routeName,
                extra: HomeDetailsArguments(postModel: widget.post),
              );
            }
          },
          child: Container(
            margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
            decoration: BoxDecoration(
              color: theme.black50,
              borderRadius: BorderRadius.all(Radius.circular(16)),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                PostHeader(
                  post: widget.post,
                  isShowActionMore: widget.isShowActionMore,
                ),
                const SizedBox(height: 8),
                if (widget.post.content.isNotEmpty)
                  _buildContentText(
                    context,
                    theme,
                    widget.post.content,
                    trimLines: 2,
                    textStyle: bodyLarge.copyWith(color: theme.neutral800),
                    moreStyle: labelLarge.copyWith(
                      color: theme.primaryGreen500,
                    ),
                  ),
                const SizedBox(height: 16),
                PostImageGallery(post: widget.post),
                const SizedBox(height: 16),
              ],
            ),
          ),
        ),
        PostActionBar(post: widget.post),
      ],
    );
  }

  Widget _buildRepostLayout(BuildContext context, ThemeData theme) {
    return Stack(
      alignment: Alignment.bottomCenter,
      children: [
        GestureDetector(
          behavior: HitTestBehavior.opaque,
          onTap: () {
            if (!widget.isNotOnTap) {
              context.push(
                RouterEnums.homeDetails.routeName,
                extra: HomeDetailsArguments(postModel: widget.post),
              );
            }
          },
          child: Container(
            margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
            decoration: BoxDecoration(
              color: theme.black50,
              borderRadius: BorderRadius.all(Radius.circular(16)),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Repost header with reposter information
                PostHeader(
                  post: widget.post,
                  isShowActionMore: widget.isShowActionMore,
                ),

                // Repost indicator
                _buildRepostIndicator(theme),

                // Original post content in a bordered container
                _buildOriginalPostContainer(context, theme),

                const SizedBox(height: 16),
              ],
            ),
          ),
        ),
        PostActionBar(post: widget.post),
      ],
    );
  }

  Widget _buildRepostIndicator(ThemeData theme) {
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: () {
        if (!widget.isNotOnTap) {
          context.push(
            RouterEnums.homeDetails.routeName,
            extra: HomeDetailsArguments(postModel: widget.post),
          );
        }
      },
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        child: Row(
          children: [
            SvgPicture.asset(
              Assets.icons.icRepost.path,
              width: 16,
              height: 16,
              colorFilter: ColorFilter.mode(theme.neutral500, BlendMode.srcIn),
            ),
            const SizedBox(width: 6),
            Text(
              'shared',
              style: labelMedium.copyWith(color: theme.neutral500),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildOriginalPostContainer(BuildContext context, ThemeData theme) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border.all(color: theme.neutral200, width: 1),
        borderRadius: BorderRadius.circular(12),
        color: theme.neutral50,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Original post author info (simplified)
          if (widget.post.user != null) _buildOriginalPostHeader(theme),

          // Original post content
          if (widget.post.content.isNotEmpty) ...[
            const SizedBox(height: 8),
            _buildContentText(
              context,
              theme,
              widget.post.content,
              trimLines: 3,
              textStyle: bodyMedium.copyWith(color: theme.neutral500),
              moreStyle: labelMedium.copyWith(color: theme.primaryGreen500),
              padding: EdgeInsets.zero,
              isRepostContent: true,
            ),
          ],

          // Original post media
          if (widget.post.mediaUrls.isNotEmpty) ...[
            const SizedBox(height: 12),
            PostImageGallery(post: widget.post),
          ],
        ],
      ),
    );
  }

  Widget _buildOriginalPostHeader(ThemeData theme) {
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: () {
        if (!widget.isNotOnTap) {
          context.push(
            RouterEnums.homeDetails.routeName,
            extra: HomeDetailsArguments(postModel: widget.post),
          );
        }
      },
      child: Row(
        children: [
          AvatarWidget(
            size: 32,
            imageUrl: widget.post.user?.avatar,
            name: widget.post.user?.fullName ?? '',
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  widget.post.user?.username ??
                      widget.post.user?.fullName ??
                      '',
                  style: titleSmall.copyWith(color: theme.neutral800),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                Text(
                  timeAgoSinceDate(widget.post.updatedAt ?? ''),
                  style: labelSmall.copyWith(color: theme.neutral400),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContentText(
    BuildContext context,
    ThemeData theme,
    String content, {
    required int trimLines,
    required TextStyle textStyle,
    required TextStyle moreStyle,
    EdgeInsets padding = const EdgeInsets.symmetric(horizontal: 12),
    bool isRepostContent = false,
  }) {
    return Padding(
      padding: padding,
      child: LayoutBuilder(
        builder: (context, constraints) {
          // Create a text painter to measure if text needs truncation
          final textPainter = TextPainter(
            text: TextSpan(text: content, style: textStyle),
            maxLines: trimLines,
            textDirection: TextDirection.ltr,
          );
          textPainter.layout(maxWidth: constraints.maxWidth);

          final bool needsTruncation = textPainter.didExceedMaxLines;
          final bool isExpanded =
              isRepostContent
                  ? _isRepostContentExpanded
                  : _isMainContentExpanded;

          return GestureDetector(
            behavior: HitTestBehavior.opaque,
            onTap: () {
              if (needsTruncation && !isExpanded) {
                // Text is truncated and not expanded - expand it
                setState(() {
                  if (isRepostContent) {
                    _isRepostContentExpanded = true;
                  } else {
                    _isMainContentExpanded = true;
                  }
                });
              } else {
                // Text is either not truncated or is expanded - navigate to detail screen
                if (!widget.isNotOnTap) {
                  context.push(
                    RouterEnums.homeDetails.routeName,
                    extra: HomeDetailsArguments(postModel: widget.post),
                  );
                }
              }
            },
            child:
                isExpanded
                    ? Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(content, style: textStyle),
                        const SizedBox(height: 4),
                        GestureDetector(
                          onTap: () {
                            // Handle "Show less" tap - collapse the text
                            setState(() {
                              if (isRepostContent) {
                                _isRepostContentExpanded = false;
                              } else {
                                _isMainContentExpanded = false;
                              }
                            });
                          },
                          child: Text(' Show less', style: moreStyle),
                        ),
                      ],
                    )
                    : ReadMoreText(
                      content,
                      trimLines: trimLines,
                      trimMode: TrimMode.Line,
                      trimCollapsedText: needsTruncation ? ' See more' : '',
                      trimExpandedText: '',
                      style: textStyle,
                      moreStyle: moreStyle,
                    ),
          );
        },
      ),
    );
  }
}
